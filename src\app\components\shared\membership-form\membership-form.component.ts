import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-membership-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './membership-form.component.html',
  styleUrl: './membership-form.component.css',
  animations: [
    trigger('fadeInOut', [
      transition(':enter', [
        style({ opacity: 0 }),
        animate('300ms ease-in', style({ opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-out', style({ opacity: 0 }))
      ])
    ]),
    trigger('slideIn', [
      transition(':enter', [
        style({ transform: 'scale(0.8)', opacity: 0 }),
        animate('300ms ease-out', style({ transform: 'scale(1)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-in', style({ transform: 'scale(0.8)', opacity: 0 }))
      ])
    ])
  ]
})
export class MembershipFormComponent implements OnInit {
  @Input() isOpen: boolean = false;
  @Output() close = new EventEmitter<void>();
  @Output() submit = new EventEmitter<any>();

  membershipForm: FormGroup;
  isSubmitting = false;
  isSubmitted = false;

  membershipTypes = [
    {
      id: 'basic',
      name: 'Basic Membership',
      price: 1000,
      benefits: [
        'Monthly newsletter',
        '10% discount on workshops',
        'Access to member-only events',
        'Priority booking for exhibitions'
      ]
    },
    {
      id: 'premium',
      name: 'Premium Membership',
      price: 2500,
      benefits: [
        'All Basic benefits',
        '20% discount on all purchases',
        'Free workshop per quarter',
        'Exclusive artist meet & greets',
        'Early access to new collections'
      ]
    },
    {
      id: 'patron',
      name: 'Patron Membership',
      price: 5000,
      benefits: [
        'All Premium benefits',
        '30% discount on all purchases',
        'Unlimited workshop access',
        'Private gallery tours',
        'Commission artwork opportunities',
        'Annual patron dinner'
      ]
    }
  ];

  constructor(private fb: FormBuilder) {
    this.membershipForm = this.fb.group({
      membershipType: ['basic', [Validators.required]],
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      address: ['', [Validators.required, Validators.minLength(10)]],
      city: ['', [Validators.required]],
      postalCode: ['', [Validators.required]],
      country: ['Nepal', [Validators.required]],
      dateOfBirth: ['', [Validators.required]],
      occupation: [''],
      interests: [''],
      howDidYouHear: [''],
      agreeToTerms: [false, [Validators.requiredTrue]],
      agreeToNewsletter: [true]
    });
  }

  ngOnInit() {
    if (this.isOpen) {
      document.body.style.overflow = 'hidden';
    }
  }

  ngOnDestroy() {
    document.body.style.overflow = 'auto';
  }

  closeForm() {
    document.body.style.overflow = 'auto';
    this.close.emit();
  }

  onBackdropClick(event: Event) {
    if (event.target === event.currentTarget) {
      this.closeForm();
    }
  }

  onSubmit() {
    if (this.membershipForm.valid) {
      this.isSubmitting = true;
      
      // Simulate API call
      setTimeout(() => {
        this.isSubmitting = false;
        this.isSubmitted = true;
        this.submit.emit(this.membershipForm.value);
        
        // Auto close after 3 seconds
        setTimeout(() => {
          this.closeForm();
          this.isSubmitted = false;
          this.membershipForm.reset();
          this.membershipForm.patchValue({
            membershipType: 'basic',
            country: 'Nepal',
            agreeToNewsletter: true
          });
        }, 3000);
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.membershipForm.controls).forEach(key => {
        this.membershipForm.get(key)?.markAsTouched();
      });
    }
  }

  getSelectedMembershipType() {
    const selectedType = this.membershipForm.get('membershipType')?.value;
    return this.membershipTypes.find(type => type.id === selectedType);
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.membershipForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.membershipForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email address';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      if (field.errors['pattern']) return 'Please enter a valid phone number';
      if (field.errors['requiredTrue']) return 'You must agree to the terms and conditions';
    }
    return '';
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }
}
