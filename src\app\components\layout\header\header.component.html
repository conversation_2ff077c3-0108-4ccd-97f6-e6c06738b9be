<header class="relative bg-gradient-to-r from-white via-primary-50 to-white shadow-lg border-b-4 border-primary-200 overflow-hidden">
  <!-- Decorative Background Pattern -->
  <div class="absolute inset-0 opacity-5">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="5">
    </app-mithila-art-background>
  </div>

  <!-- Decorative Border Elements -->
  <div class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-600 via-secondary-500 to-primary-600"></div>

  <div class="container mx-auto px-4 relative z-10">
    <div class="flex justify-between items-center py-4">
      <!-- Enhanced Logo -->
      <a routerLink="/" class="flex items-center space-x-3 group">
        <div class="relative">
          <!-- Logo Background with <PERSON><PERSON><PERSON> -->
          <div class="w-12 h-12 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
            <span class="text-white font-bold text-xl drop-shadow-md">M</span>
          </div>

          <!-- Decorative Ring -->
          <div class="absolute -inset-1 bg-gradient-to-r from-primary-300 to-secondary-300 rounded-full opacity-0 group-hover:opacity-30 transition-opacity duration-300 blur-sm"></div>

          <!-- Small Decorative Element -->
          <app-mithila-decorative-element
            [primaryColor]="'#F4B400'"
            [secondaryColor]="'#C1440E'"
            [type]="'lotus'"
            position="absolute -top-1 -right-1"
            classes="opacity-60 group-hover:opacity-100 transition-opacity duration-300"
            size="16px">
          </app-mithila-decorative-element>
        </div>

        <div class="flex flex-col">
          <span class="text-2xl font-bold bg-gradient-to-r from-primary-700 to-secondary-600 bg-clip-text text-transparent group-hover:from-primary-600 group-hover:to-secondary-500 transition-all duration-300">
            Mithilani Ghar
          </span>
          <span class="text-xs text-gray-500 font-medium tracking-wide">
            Heritage • Art • Culture
          </span>
        </div>
      </a>

      <!-- Enhanced Desktop Navigation -->
      <nav class="hidden md:flex items-center space-x-6">
        <!-- Navigation Links with Fast Hover Effects -->
        <a routerLink="/" routerLinkActive="text-primary-600 nav-active" [routerLinkActiveOptions]="{exact: true}"
          class="relative px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-150 font-medium group">
          <span class="relative z-10">Home</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full nav-active:w-full"></div>
        </a>

      <a routerLink="/products" routerLinkActive="text-primary-600 nav-active"
          class="relative px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-150 font-medium group">
          <span class="relative z-10">Products</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full nav-active:w-full"></div>
        </a>

        <a routerLink="/gallery" routerLinkActive="text-primary-600 nav-active"
          class="relative px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-150 font-medium group">
          <span class="relative z-10">Gallery</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full nav-active:w-full"></div>
        </a>

        <a routerLink="/artists" routerLinkActive="text-primary-600 nav-active"
          class="relative px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-150 font-medium group">
          <span class="relative z-10">Artists</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full nav-active:w-full"></div>
        </a>

        <a routerLink="/about" routerLinkActive="text-primary-600 nav-active"
          class="relative px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-150 font-medium group">
          <span class="relative z-10">About</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full nav-active:w-full"></div>
        </a>

        <a routerLink="/contact" routerLinkActive="text-primary-600 nav-active"
          class="relative px-3 py-2 text-gray-700 hover:text-primary-600 transition-colors duration-150 font-medium group">
          <span class="relative z-10">Contact</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-200 group-hover:w-full  nav-active:w-full "></div>
        </a>

        <!-- Divider -->
        <div class="h-6 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>



        <!-- Enhanced Cart Icon -->
        <a routerLink="/cart" class="relative group">
          <div class="relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 rounded-full hover:bg-primary-50">
            <svg class="h-6 w-6 transform group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
            </svg>

            <!-- Cart Count Badge -->
            <span *ngIf="cartItemCount > 0"
                  class="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold shadow-lg animate-pulse">
              {{cartItemCount}}
            </span>

            <!-- Decorative Ring -->
            <div class="absolute inset-0 rounded-full border-2 border-primary-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-110"></div>
          </div>
        </a>
      </nav>

      <!-- Enhanced Mobile menu button and cart -->
      <div class="md:hidden flex items-center space-x-3">
        <!-- Mobile Cart Icon -->
        <a routerLink="/cart" class="relative group">
          <div class="relative p-2 text-gray-600 hover:text-primary-600 transition-all duration-300 rounded-lg hover:bg-primary-50">
            <svg class="h-6 w-6 transform group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
            </svg>
            <span *ngIf="cartItemCount > 0"
                  class="absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold shadow-lg">
              {{cartItemCount}}
            </span>
          </div>
        </a>

        <!-- Enhanced Mobile Menu Button -->
        <button type="button" (click)="toggleMenu()"
                class="relative p-2 text-gray-600 hover:text-primary-600 focus:outline-none rounded-lg hover:bg-primary-50 transition-all duration-300 group">
          <svg class="h-6 w-6 transform group-hover:scale-110 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path *ngIf="!isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            <path *ngIf="isMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>

          <!-- Decorative Ring -->
          <div class="absolute inset-0 rounded-lg border border-primary-300 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        </button>
      </div>
    </div>

    <!-- Enhanced Mobile Navigation -->
    <div *ngIf="isMenuOpen" class="md:hidden py-4 border-t border-gray-200 bg-white shadow-lg">
      <nav class="flex flex-col space-y-1 px-4">
        <a routerLink="/" routerLinkActive="text-primary-600" [routerLinkActiveOptions]="{exact: true}"
          class="px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-150 rounded-lg font-medium flex items-center">
          <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          Home
        </a>

        <a routerLink="/products" routerLinkActive="text-primary-600"
          class="px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-150 rounded-lg font-medium flex items-center">
          <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
          </svg>
          Products
        </a>

        <a routerLink="/gallery" routerLinkActive="text-primary-600"
          class="px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-150 rounded-lg font-medium flex items-center">
          <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          Gallery
        </a>

        <a routerLink="/artists" routerLinkActive="text-primary-600"
          class="px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-150 rounded-lg font-medium flex items-center">
          <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
          </svg>
          Artists
        </a>

        <a routerLink="/about" routerLinkActive="text-primary-600"
          class="px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-150 rounded-lg font-medium flex items-center">
          <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          About
        </a>

        <a routerLink="/contact" routerLinkActive="text-primary-600"
          class="px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-primary-50 transition-colors duration-150 rounded-lg font-medium flex items-center">
          <svg class="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          Contact
        </a>
      </nav>
    </div>
  </div>
</header>


