/* Artist Application Form Styles */

/* Modal backdrop animation */
.modal-backdrop {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Modal content animation */
.modal-content {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form section headers */
.section-header {
  border-bottom: 2px solid #f3f4f6;
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

/* Custom checkbox styles */
input[type="checkbox"] {
  border-radius: 0.25rem;
  border: 2px solid #d1d5db;
  transition: all 0.2s ease;
}

input[type="checkbox"]:checked {
  background-color: #C1440E;
  border-color: #C1440E;
}

input[type="checkbox"]:focus {
  ring-color: #C1440E;
  ring-opacity: 0.5;
}

/* Form field focus states */
input:focus,
select:focus,
textarea:focus {
  border-color: #C1440E;
  box-shadow: 0 0 0 3px rgba(193, 68, 14, 0.1);
}

/* Error states */
.border-red-500 {
  border-color: #ef4444 !important;
}

/* Success animation */
.success-icon {
  animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Loading spinner */
.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    max-height: calc(100vh - 2rem);
  }
  
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  
  .grid-cols-3 {
    grid-template-columns: 1fr 1fr;
  }
}

/* Print styles */
@media print {
  .modal-backdrop {
    display: none;
  }
}
