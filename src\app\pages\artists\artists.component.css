/* Artists page specific styles */

/* Animation for floating elements */
@keyframes float-slow {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-6px); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.5; }
}

@keyframes border-gradient {
  0%, 100% { opacity: 0.7; }
  50% { opacity: 0.3; }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-border-gradient {
  animation: border-gradient 4s ease-in-out infinite;
}

/* Fade-in animation */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fade-in {
  animation: fade-in 1.5s ease-out forwards;
}

/* Artist card hover effects */
.artist-card {
  transition: all 0.3s ease;
}

.artist-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Workshop card styles */
.workshop-card {
  position: relative;
  overflow: hidden;
}

.workshop-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(193, 68, 14, 0.05), rgba(244, 180, 0, 0.05));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: 0;
}

.workshop-card:hover::before {
  transform: translateX(0);
}

/* Line clamp for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.3s ease;
}

.btn:hover {
  transform: translateY(-2px);
}

/* Spotlight section styles */
.achievement-item {
  position: relative;
  padding-left: 1.5rem;
}

.achievement-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #C1440E;
  font-size: 1.25rem;
  line-height: 1;
}

/* Enhanced Responsive Design for Artists Component */

/* Mobile styles */
@media (max-width: 640px) {
  /* Artist cards */
  .artist-card {
    margin-bottom: 1.5rem;
    padding: 1rem;
  }

  .artist-card:hover {
    transform: translateY(-4px);
  }

  /* Artist image */
  .artist-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
  }

  /* Artist info */
  .artist-info {
    padding: 1rem;
    text-align: center;
  }

  .artist-name {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .artist-role {
    font-size: 0.875rem;
    margin-bottom: 0.75rem;
  }

  .artist-bio {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  /* Artwork grid */
  .artwork-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  /* Workshop cards */
  .workshop-card {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  /* Button adjustments */
  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    width: 100%;
    margin-bottom: 0.5rem;
  }

  /* Achievement items */
  .achievement-item {
    padding-left: 1rem;
    margin-bottom: 0.75rem;
  }

  /* Social links */
  .social-links {
    justify-content: center;
    gap: 0.75rem;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

/* Tablet styles */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Artist cards grid */
  .artists-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  .artist-card {
    padding: 1.25rem;
  }

  .artist-image {
    height: 250px;
  }

  /* Artwork grid */
  .artwork-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }

  /* Workshop cards */
  .workshop-card {
    padding: 1.25rem;
  }

  /* Button adjustments */
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.9rem;
  }

  /* Social links */
  .social-link {
    width: 44px;
    height: 44px;
    font-size: 1.125rem;
  }
}

/* Desktop styles */
@media (min-width: 1025px) {
  /* Artist cards grid */
  .artists-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }

  .artist-card {
    padding: 1.5rem;
  }

  .artist-image {
    height: 300px;
  }

  /* Artwork grid */
  .artwork-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  /* Workshop cards */
  .workshop-card {
    padding: 1.5rem;
  }

  /* Enhanced hover effects for desktop */
  .artist-card:hover {
    transform: translateY(-12px) scale(1.02);
  }

  .workshop-card:hover {
    transform: translateY(-6px);
  }

  /* Social links */
  .social-link {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
  }
}

/* Large desktop styles */
@media (min-width: 1440px) {
  .artists-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2.5rem;
  }

  .artwork-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }

  .artist-card {
    padding: 2rem;
  }

  .workshop-card {
    padding: 2rem;
  }
}

/* Touch-friendly improvements for mobile */
@media (max-width: 768px) and (pointer: coarse) {
  .btn,
  .social-link {
    min-height: 44px;
    min-width: 44px;
  }

  /* Larger touch targets for interactive elements */
  .artist-card {
    min-height: 44px;
  }

  .workshop-card {
    min-height: 44px;
  }
}

/* Landscape mobile adjustments */
@media (max-width: 768px) and (orientation: landscape) {
  .artists-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .artist-image {
    height: 180px;
  }

  .artist-info {
    padding: 0.75rem;
  }
}

/* Print styles */
@media print {
  .artist-card,
  .workshop-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #ddd;
  }

  .btn,
  .social-links {
    display: none;
  }

  .artist-image {
    height: auto;
    max-height: 200px;
  }
}
