<footer class="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white overflow-hidden">
  <!-- Decorative Top Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [position]="'top'"
    [height]="'6px'"
  ></app-mithila-border>

  <!-- Background Pattern -->
  <div class="absolute inset-0 opacity-5">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="5">
    </app-mithila-art-background>
  </div>

  <!-- Decorative Elements -->
  <app-mithila-decorative-element
    [primaryColor]="'#F4B400'"
    [secondaryColor]="'#C1440E'"
    [type]="'lotus'"
    position="absolute top-8 right-8"
    classes="opacity-10"
    size="120px">
  </app-mithila-decorative-element>

  <app-mithila-decorative-element
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [type]="'peacock'"
    position="absolute bottom-8 left-8"
    classes="opacity-10"
    size="100px">
  </app-mithila-decorative-element>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
    <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
      <!-- Enhanced Logo and About -->
      <div class="col-span-1 md:col-span-1">
        <div class="flex items-center mb-6 group">
          <div class="relative">
            <!-- Enhanced Logo -->
            <div class="w-14 h-14 bg-gradient-to-br from-primary-600 to-secondary-600 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
              <span class="text-white font-bold text-2xl drop-shadow-md">M</span>
            </div>

            <!-- Decorative Ring -->
            <div class="absolute -inset-1 bg-gradient-to-r from-primary-300 to-secondary-300 rounded-full opacity-0 group-hover:opacity-30 transition-opacity duration-300 blur-sm"></div>

            <!-- Small Decorative Element -->
            <app-mithila-decorative-element
              [primaryColor]="'#F4B400'"
              [secondaryColor]="'#C1440E'"
              [type]="'lotus'"
              position="absolute -top-1 -right-1"
              classes="opacity-60 group-hover:opacity-100 transition-opacity duration-300"
              size="20px">
            </app-mithila-decorative-element>
          </div>

          <div class="ml-4">
            <span class="text-2xl font-bold bg-gradient-to-r from-primary-300 to-secondary-300 bg-clip-text text-transparent group-hover:from-primary-200 group-hover:to-secondary-200 transition-all duration-300">
              Mithilani Ghar
            </span>
            <div class="text-xs text-gray-400 font-medium tracking-wide mt-1">
              Heritage • Art • Culture
            </div>
          </div>
        </div>

        <p class="text-gray-300 mb-6 leading-relaxed">
          Preserving and promoting the rich artistic heritage of Mithila region through traditional arts, crafts, and community engagement.
        </p>

        <!-- Enhanced Social Media Links -->
        <div class="flex space-x-4">
          <a href="https://facebook.com" target="_blank"
             class="group relative p-3 bg-gray-800 rounded-full hover:bg-primary-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg">
            <span class="sr-only">Facebook</span>
            <svg class="h-5 w-5 text-gray-400 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z" clip-rule="evenodd" />
            </svg>
            <div class="absolute inset-0 rounded-full border border-primary-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-110"></div>
          </a>

          <a href="https://instagram.com" target="_blank"
             class="group relative p-3 bg-gray-800 rounded-full hover:bg-gradient-to-r hover:from-purple-600 hover:to-pink-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg">
            <span class="sr-only">Instagram</span>
            <svg class="h-5 w-5 text-gray-400 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z" clip-rule="evenodd" />
            </svg>
            <div class="absolute inset-0 rounded-full border border-pink-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-110"></div>
          </a>

          <a href="https://youtube.com" target="_blank"
             class="group relative p-3 bg-gray-800 rounded-full hover:bg-red-600 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg">
            <span class="sr-only">YouTube</span>
            <svg class="h-5 w-5 text-gray-400 group-hover:text-white transition-colors duration-300" fill="currentColor" viewBox="0 0 24 24">
              <path fill-rule="evenodd" d="M19.812 5.418c.861.23 1.538.907 1.768 1.768C21.998 8.746 22 12 22 12s0 3.255-.418 4.814a2.504 2.504 0 0 1-1.768 1.768c-1.56.419-7.814.419-7.814.419s-6.255 0-7.814-.419a2.505 2.505 0 0 1-1.768-1.768C2 15.255 2 12 2 12s0-3.255.417-4.814a2.507 2.507 0 0 1 1.768-1.768C5.744 5 11.998 5 11.998 5s6.255 0 7.814.418ZM15.194 12 10 15V9l5.194 3Z" clip-rule="evenodd" />
            </svg>
            <div class="absolute inset-0 rounded-full border border-red-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300 scale-110"></div>
          </a>
        </div>
      </div>

      <!-- Enhanced Quick Links -->
      <div class="col-span-1">
        <h3 class="text-xl font-bold mb-6 text-white flex items-center">
          <svg class="w-5 h-5 mr-2 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
          </svg>
          Quick Links
        </h3>
        <ul class="space-y-3">
          <li>
            <a routerLink="/" class="group flex items-center text-gray-300 hover:text-primary-300 transition-all duration-300 transform hover:translate-x-2">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              Home
            </a>
          </li>
          <li>
            <a routerLink="/products" class="group flex items-center text-gray-300 hover:text-primary-300 transition-all duration-300 transform hover:translate-x-2">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              Products
            </a>
          </li>
          <li>
            <a routerLink="/gallery" class="group flex items-center text-gray-300 hover:text-primary-300 transition-all duration-300 transform hover:translate-x-2">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              Gallery
            </a>
          </li>
          <li>
            <a routerLink="/artists" class="group flex items-center text-gray-300 hover:text-primary-300 transition-all duration-300 transform hover:translate-x-2">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              Artists
            </a>
          </li>
          <li>
            <a routerLink="/about" class="group flex items-center text-gray-300 hover:text-primary-300 transition-all duration-300 transform hover:translate-x-2">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              About Us
            </a>
          </li>
          <li>
            <a routerLink="/contact" class="group flex items-center text-gray-300 hover:text-primary-300 transition-all duration-300 transform hover:translate-x-2">
              <span class="w-2 h-2 bg-primary-500 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              Contact
            </a>
          </li>
        </ul>
      </div>

      <!-- Enhanced Contact Info -->
      <div class="col-span-1">
        <h3 class="text-xl font-bold mb-6 text-white flex items-center">
          <svg class="w-5 h-5 mr-2 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
          </svg>
          Contact Us
        </h3>
        <ul class="space-y-4">
          <li class="group">
            <div class="flex items-start p-3 rounded-lg hover:bg-gray-800 transition-colors duration-300">
              <div class="flex-shrink-0 w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mr-3 group-hover:bg-primary-500 transition-colors duration-300">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400 mb-1">Address</p>
                <p class="text-gray-300 text-sm leading-relaxed">Barahbigha, Janaki Mandir Marg<br>Janakpurdham-08, Dhanusha, Nepal</p>
              </div>
            </div>
          </li>

          <li class="group">
            <div class="flex items-start p-3 rounded-lg hover:bg-gray-800 transition-colors duration-300">
              <div class="flex-shrink-0 w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3 group-hover:bg-green-500 transition-colors duration-300">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400 mb-1">Phone</p>
                <p class="text-gray-300 text-sm">+977-9814830580</p>
                <p class="text-gray-300 text-sm">+977-9821762884</p>
              </div>
            </div>
          </li>

          <li class="group">
            <div class="flex items-start p-3 rounded-lg hover:bg-gray-800 transition-colors duration-300">
              <div class="flex-shrink-0 w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3 group-hover:bg-blue-500 transition-colors duration-300">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400 mb-1">Email</p>
                <p class="text-gray-300 text-sm">mithilanighar&#64;gmail.com</p>
              </div>
            </div>
          </li>

          <li class="group">
            <div class="flex items-start p-3 rounded-lg hover:bg-gray-800 transition-colors duration-300">
              <div class="flex-shrink-0 w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3 group-hover:bg-purple-500 transition-colors duration-300">
                <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p class="text-sm text-gray-400 mb-1">Hours</p>
                <p class="text-gray-300 text-sm">Daily: 9:00 AM - 8:00 PM</p>
                <p class="text-xs text-green-400 flex items-center mt-1">
                  <span class="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Currently Open
                </p>
              </div>
            </div>
          </li>
        </ul>
      </div>


    </div>

    <!-- Enhanced Bottom Footer -->
    <div class="relative mt-16 pt-8">
      <!-- Decorative Border -->
      <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary-500 to-transparent"></div>

      <div class="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
        <!-- Copyright -->
        <div class="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4">
          <p class="text-gray-400 text-sm">© {{currentYear}} Mithilani Ghar. All rights reserved.</p>
          <div class="hidden sm:block w-px h-4 bg-gray-600"></div>
          <p class="text-gray-500 text-xs">Preserving Mithila Heritage Since 2015</p>
        </div>

        <!-- Links -->
        <div class="flex flex-wrap justify-center lg:justify-end items-center space-x-6 text-sm">
          <a routerLink="/privacy-policy" class="text-gray-400 hover:text-primary-300 transition-colors duration-300 flex items-center group">
            <svg class="w-4 h-4 mr-1 group-hover:text-primary-400 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
            Privacy Policy
          </a>
          <a routerLink="/terms-of-service" class="text-gray-400 hover:text-primary-300 transition-colors duration-300 flex items-center group">
            <svg class="w-4 h-4 mr-1 group-hover:text-primary-400 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Terms of Service
          </a>
        </div>
      </div>

      <!-- Made with Love -->
      <div class="text-center mt-8 pt-6 border-t border-gray-800">
        <p class="text-gray-500 text-xs flex items-center justify-center">
          Made with
          <svg class="w-4 h-4 mx-1 text-red-500 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
          </svg>
          for preserving Mithila art heritage
        </p>
      </div>
    </div>
  </div>
</footer>
