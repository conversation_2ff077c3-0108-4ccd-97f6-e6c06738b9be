import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { DataService, Artist } from '../../services/data.service';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-artist-detail',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './artist-detail.component.html',
  styleUrl: './artist-detail.component.css'
})
export class ArtistDetailComponent implements OnInit {
  artistId: string = '';
  artist: Artist | null = null;
  artistArtworks: any[] = [];
  artistWorkshops: any[] = [];
  relatedArtists: Artist[] = [];

  constructor(
    private route: ActivatedRoute,
    private dataService: DataService
  ) {
    this.route.params.subscribe(params => {
      this.artistId = params['id'];
      this.loadArtistData();
    });
  }

  ngOnInit(): void {
    this.loadArtistData();
  }

  loadArtistData(): void {
    // Find the artist by ID
    this.artist = this.dataService.getArtistById(this.artistId) || null;

    if (this.artist) {
      // Get artworks by this artist (if available in the artist data)
      this.artistArtworks = this.artist.artworks || [];

      // Get related artists (other featured artists excluding current one)
      this.relatedArtists = this.dataService.getArtists()
        .filter(a => a.id !== this.artistId)
        .slice(0, 2); // Limit to 2 related artists
    }
  }
}
