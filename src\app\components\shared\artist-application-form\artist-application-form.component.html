<!-- Artist Application Form Modal -->
<div *ngIf="isOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
  <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
    
    <!-- Header -->
    <div class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white p-6 rounded-t-2xl">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-3xl font-bold mb-2">{{title}}</h2>
          <p class="text-primary-100">Apply to showcase your Mithila art and join our artist community</p>
        </div>
        <button 
          class="text-white hover:text-gray-300 transition-colors duration-200"
          (click)="closeForm()"
          aria-label="Close form">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Success Message -->
    <div *ngIf="isSubmitted" class="p-8 text-center">
      <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-2">Application Submitted!</h3>
      <p class="text-gray-600">Thank you for applying to join our artist community. We'll review your application and contact you within 5-7 business days.</p>
    </div>

    <!-- Form Content -->
    <div *ngIf="!isSubmitted" class="p-6">
      <form [formGroup]="artistForm" (ngSubmit)="onSubmit()">
        
        <!-- Personal Information -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            Personal Information
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <!-- First Name -->
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
              <input type="text" id="firstName" formControlName="firstName"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('firstName')"
                     placeholder="Enter your first name">
              <p *ngIf="isFieldInvalid('firstName')" class="text-red-500 text-xs mt-1">{{getFieldError('firstName')}}</p>
            </div>

            <!-- Last Name -->
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
              <input type="text" id="lastName" formControlName="lastName"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('lastName')"
                     placeholder="Enter your last name">
              <p *ngIf="isFieldInvalid('lastName')" class="text-red-500 text-xs mt-1">{{getFieldError('lastName')}}</p>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input type="email" id="email" formControlName="email"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('email')"
                     placeholder="Enter your email address">
              <p *ngIf="isFieldInvalid('email')" class="text-red-500 text-xs mt-1">{{getFieldError('email')}}</p>
            </div>

            <!-- Phone -->
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
              <input type="tel" id="phone" formControlName="phone"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('phone')"
                     placeholder="9814830580">
              <p *ngIf="isFieldInvalid('phone')" class="text-red-500 text-xs mt-1">{{getFieldError('phone')}}</p>
            </div>

            <!-- Date of Birth -->
            <div>
              <label for="dateOfBirth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth *</label>
              <input type="date" id="dateOfBirth" formControlName="dateOfBirth"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('dateOfBirth')">
              <p *ngIf="isFieldInvalid('dateOfBirth')" class="text-red-500 text-xs mt-1">{{getFieldError('dateOfBirth')}}</p>
            </div>

            <!-- Artist Name -->
            <div>
              <label for="artistName" class="block text-sm font-medium text-gray-700 mb-1">Artist Name/Pen Name *</label>
              <input type="text" id="artistName" formControlName="artistName"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('artistName')"
                     placeholder="Name you want to be known as">
              <p *ngIf="isFieldInvalid('artistName')" class="text-red-500 text-xs mt-1">{{getFieldError('artistName')}}</p>
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            </svg>
            Address Information
          </h3>
          <div class="grid grid-cols-1 gap-4">
            
            <!-- Address -->
            <div>
              <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
              <textarea id="address" formControlName="address" rows="2"
                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('address')"
                        placeholder="Enter your full address"></textarea>
              <p *ngIf="isFieldInvalid('address')" class="text-red-500 text-xs mt-1">{{getFieldError('address')}}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- City -->
              <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                <input type="text" id="city" formControlName="city"
                       class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                       [class.border-red-500]="isFieldInvalid('city')"
                       placeholder="Enter your city">
                <p *ngIf="isFieldInvalid('city')" class="text-red-500 text-xs mt-1">{{getFieldError('city')}}</p>
              </div>

              <!-- Postal Code -->
              <div>
                <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-1">Postal Code *</label>
                <input type="text" id="postalCode" formControlName="postalCode"
                       class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                       [class.border-red-500]="isFieldInvalid('postalCode')"
                       placeholder="Enter postal code">
                <p *ngIf="isFieldInvalid('postalCode')" class="text-red-500 text-xs mt-1">{{getFieldError('postalCode')}}</p>
              </div>

              <!-- Country -->
              <div>
                <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                <select id="country" formControlName="country"
                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('country')">
                  <option value="Nepal">Nepal</option>
                  <option value="India">India</option>
                  <option value="Bangladesh">Bangladesh</option>
                  <option value="Other">Other</option>
                </select>
                <p *ngIf="isFieldInvalid('country')" class="text-red-500 text-xs mt-1">{{getFieldError('country')}}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Artist Experience -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            Artist Experience
          </h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

            <!-- Experience Level -->
            <div>
              <label for="experienceLevel" class="block text-sm font-medium text-gray-700 mb-1">Experience Level *</label>
              <select id="experienceLevel" formControlName="experienceLevel"
                      class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      [class.border-red-500]="isFieldInvalid('experienceLevel')">
                <option value="">Select your experience level</option>
                <option *ngFor="let level of experienceLevels" [value]="level.value">{{level.label}}</option>
              </select>
              <p *ngIf="isFieldInvalid('experienceLevel')" class="text-red-500 text-xs mt-1">{{getFieldError('experienceLevel')}}</p>
            </div>

            <!-- Years of Experience -->
            <div>
              <label for="yearsOfExperience" class="block text-sm font-medium text-gray-700 mb-1">Years of Experience *</label>
              <input type="number" id="yearsOfExperience" formControlName="yearsOfExperience"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('yearsOfExperience')"
                     placeholder="0" min="0">
              <p *ngIf="isFieldInvalid('yearsOfExperience')" class="text-red-500 text-xs mt-1">{{getFieldError('yearsOfExperience')}}</p>
            </div>
          </div>

          <!-- Art Styles -->
          <div class="mt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Art Styles You Practice *</label>
            <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
              <div *ngFor="let style of artStyles" class="flex items-center">
                <input type="checkbox"
                       [id]="'style-' + style"
                       (change)="onArtStyleChange(style, $event)"
                       class="text-primary-600 focus:ring-primary-500">
                <label [for]="'style-' + style" class="ml-2 text-sm text-gray-700">{{style}}</label>
              </div>
            </div>
            <p *ngIf="isFieldInvalid('artStyles')" class="text-red-500 text-xs mt-1">Please select at least one art style</p>
          </div>
        </div>

        <!-- Portfolio Information -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            Portfolio Information
          </h3>

          <!-- Portfolio Description -->
          <div class="mb-4">
            <label for="portfolioDescription" class="block text-sm font-medium text-gray-700 mb-1">Describe Your Artwork *</label>
            <textarea id="portfolioDescription" formControlName="portfolioDescription" rows="4"
                      class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      [class.border-red-500]="isFieldInvalid('portfolioDescription')"
                      placeholder="Describe your artistic style, themes, techniques, and notable works. Minimum 50 characters."></textarea>
            <p *ngIf="isFieldInvalid('portfolioDescription')" class="text-red-500 text-xs mt-1">{{getFieldError('portfolioDescription')}}</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Portfolio Website -->
            <div>
              <label for="portfolioWebsite" class="block text-sm font-medium text-gray-700 mb-1">Portfolio Website (Optional)</label>
              <input type="url" id="portfolioWebsite" formControlName="portfolioWebsite"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     placeholder="https://your-portfolio.com">
            </div>

            <!-- Social Media -->
            <div>
              <label for="socialMediaLinks" class="block text-sm font-medium text-gray-700 mb-1">Social Media Links (Optional)</label>
              <input type="text" id="socialMediaLinks" formControlName="socialMediaLinks"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     placeholder="Instagram, Facebook, etc.">
            </div>
          </div>
        </div>

        <!-- Motivation -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
            Motivation & Goals
          </h3>

          <!-- Why Join -->
          <div class="mb-4">
            <label for="whyJoin" class="block text-sm font-medium text-gray-700 mb-1">Why do you want to join Mithilani Ghar? *</label>
            <textarea id="whyJoin" formControlName="whyJoin" rows="3"
                      class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      [class.border-red-500]="isFieldInvalid('whyJoin')"
                      placeholder="Tell us your motivation for joining our platform. Minimum 100 characters."></textarea>
            <p *ngIf="isFieldInvalid('whyJoin')" class="text-red-500 text-xs mt-1">{{getFieldError('whyJoin')}}</p>
          </div>

          <!-- Goals -->
          <div>
            <label for="goals" class="block text-sm font-medium text-gray-700 mb-1">What are your artistic goals? *</label>
            <textarea id="goals" formControlName="goals" rows="2"
                      class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      [class.border-red-500]="isFieldInvalid('goals')"
                      placeholder="Share your short-term and long-term artistic goals."></textarea>
            <p *ngIf="isFieldInvalid('goals')" class="text-red-500 text-xs mt-1">{{getFieldError('goals')}}</p>
          </div>
        </div>

        <!-- Availability & Participation -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 mr-2 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Availability & Participation
          </h3>

          <!-- Availability -->
          <div class="mb-4">
            <label for="availability" class="block text-sm font-medium text-gray-700 mb-1">How much time can you dedicate to creating art? *</label>
            <select id="availability" formControlName="availability"
                    class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    [class.border-red-500]="isFieldInvalid('availability')">
              <option value="">Select your availability</option>
              <option value="part-time">Part-time (few hours per week)</option>
              <option value="regular">Regular (several hours per week)</option>
              <option value="full-time">Full-time (dedicated artist)</option>
            </select>
            <p *ngIf="isFieldInvalid('availability')" class="text-red-500 text-xs mt-1">{{getFieldError('availability')}}</p>
          </div>

          <!-- Additional Participation -->
          <div class="space-y-2">
            <div class="flex items-center">
              <input type="checkbox" id="canTeach" formControlName="canTeach"
                     class="text-primary-600 focus:ring-primary-500">
              <label for="canTeach" class="ml-2 text-sm text-gray-700">
                I'm interested in teaching workshops or classes
              </label>
            </div>

            <div class="flex items-center">
              <input type="checkbox" id="canParticipateEvents" formControlName="canParticipateEvents"
                     class="text-primary-600 focus:ring-primary-500">
              <label for="canParticipateEvents" class="ml-2 text-sm text-gray-700">
                I'm interested in participating in exhibitions and events
              </label>
            </div>
          </div>
        </div>

        <!-- Terms and Conditions -->
        <div class="mb-6">
          <div class="space-y-3">
            <div class="flex items-start">
              <input type="checkbox" id="agreeToTerms" formControlName="agreeToTerms"
                     class="mt-1 text-primary-600 focus:ring-primary-500"
                     [class.border-red-500]="isFieldInvalid('agreeToTerms')">
              <label for="agreeToTerms" class="ml-2 text-sm text-gray-700">
                I agree to the <a href="#" class="text-primary-600 hover:text-primary-700">Terms and Conditions</a> and <a href="#" class="text-primary-600 hover:text-primary-700">Privacy Policy</a> *
              </label>
            </div>
            <p *ngIf="isFieldInvalid('agreeToTerms')" class="text-red-500 text-xs">{{getFieldError('agreeToTerms')}}</p>

            <div class="flex items-start">
              <input type="checkbox" id="agreeToCommission" formControlName="agreeToCommission"
                     class="mt-1 text-primary-600 focus:ring-primary-500"
                     [class.border-red-500]="isFieldInvalid('agreeToCommission')">
              <label for="agreeToCommission" class="ml-2 text-sm text-gray-700">
                I understand and agree to the commission structure (platform takes 15% commission on sales) *
              </label>
            </div>
            <p *ngIf="isFieldInvalid('agreeToCommission')" class="text-red-500 text-xs">{{getFieldError('agreeToCommission')}}</p>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-4">
          <button type="button" 
                  (click)="closeForm()"
                  class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            Cancel
          </button>
          <button type="submit" 
                  [disabled]="isSubmitting"
                  class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center">
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{isSubmitting ? 'Submitting...' : 'Submit Application'}}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
