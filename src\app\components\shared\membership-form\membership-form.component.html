<!-- Membership Form Modal -->
<div *ngIf="isOpen" 
     class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
     [@fadeInOut]
     (click)="onBackdropClick($event)">
  
  <div class="bg-white rounded-lg shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto" [@slideIn]>
    <!-- Header -->
    <div class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white p-6 rounded-t-lg">
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold mb-2">Become a Member</h2>
          <p class="text-primary-100">Join our community and support Mithila art</p>
        </div>
        <button 
          class="text-white hover:text-gray-300 transition-colors duration-200"
          (click)="closeForm()"
          aria-label="Close form">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Success Message -->
    <div *ngIf="isSubmitted" class="p-8 text-center">
      <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
      </div>
      <h3 class="text-xl font-bold text-gray-900 mb-2">Application Submitted!</h3>
      <p class="text-gray-600">Thank you for your interest in becoming a member. We'll contact you soon with next steps.</p>
    </div>

    <!-- Form Content -->
    <div *ngIf="!isSubmitted" class="p-6">
      <form [formGroup]="membershipForm" (ngSubmit)="onSubmit()">
        
        <!-- Membership Type Selection -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Choose Your Membership</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div *ngFor="let type of membershipTypes" 
                 class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
                 [class.border-primary-500]="membershipForm.get('membershipType')?.value === type.id"
                 [class.bg-primary-50]="membershipForm.get('membershipType')?.value === type.id"
                 [class.border-gray-200]="membershipForm.get('membershipType')?.value !== type.id"
                 (click)="membershipForm.patchValue({membershipType: type.id})">
              
              <div class="flex items-center mb-2">
                <input type="radio" 
                       [value]="type.id" 
                       formControlName="membershipType"
                       class="text-primary-600 focus:ring-primary-500">
                <label class="ml-2 font-semibold text-gray-900">{{type.name}}</label>
              </div>
              
              <p class="text-2xl font-bold text-primary-600 mb-3">{{formatPrice(type.price)}}/year</p>
              
              <ul class="space-y-1">
                <li *ngFor="let benefit of type.benefits" class="text-sm text-gray-600 flex items-start">
                  <svg class="w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  {{benefit}}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Personal Information -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            
            <!-- First Name -->
            <div>
              <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">First Name *</label>
              <input type="text" id="firstName" formControlName="firstName"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('firstName')"
                     placeholder="Enter your first name">
              <p *ngIf="isFieldInvalid('firstName')" class="text-red-500 text-xs mt-1">{{getFieldError('firstName')}}</p>
            </div>

            <!-- Last Name -->
            <div>
              <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">Last Name *</label>
              <input type="text" id="lastName" formControlName="lastName"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('lastName')"
                     placeholder="Enter your last name">
              <p *ngIf="isFieldInvalid('lastName')" class="text-red-500 text-xs mt-1">{{getFieldError('lastName')}}</p>
            </div>

            <!-- Email -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
              <input type="email" id="email" formControlName="email"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('email')"
                     placeholder="Enter your email address">
              <p *ngIf="isFieldInvalid('email')" class="text-red-500 text-xs mt-1">{{getFieldError('email')}}</p>
            </div>

            <!-- Phone -->
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
              <input type="tel" id="phone" formControlName="phone"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('phone')"
                     placeholder="9814830580">
              <p *ngIf="isFieldInvalid('phone')" class="text-red-500 text-xs mt-1">{{getFieldError('phone')}}</p>
            </div>

            <!-- Date of Birth -->
            <div>
              <label for="dateOfBirth" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth *</label>
              <input type="date" id="dateOfBirth" formControlName="dateOfBirth"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     [class.border-red-500]="isFieldInvalid('dateOfBirth')">
              <p *ngIf="isFieldInvalid('dateOfBirth')" class="text-red-500 text-xs mt-1">{{getFieldError('dateOfBirth')}}</p>
            </div>

            <!-- Occupation -->
            <div>
              <label for="occupation" class="block text-sm font-medium text-gray-700 mb-1">Occupation</label>
              <input type="text" id="occupation" formControlName="occupation"
                     class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                     placeholder="Your occupation">
            </div>
          </div>
        </div>

        <!-- Address Information -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Address Information</h3>
          <div class="grid grid-cols-1 gap-4">
            
            <!-- Address -->
            <div>
              <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
              <textarea id="address" formControlName="address" rows="2"
                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('address')"
                        placeholder="Enter your full address"></textarea>
              <p *ngIf="isFieldInvalid('address')" class="text-red-500 text-xs mt-1">{{getFieldError('address')}}</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- City -->
              <div>
                <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                <input type="text" id="city" formControlName="city"
                       class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                       [class.border-red-500]="isFieldInvalid('city')"
                       placeholder="Enter your city">
                <p *ngIf="isFieldInvalid('city')" class="text-red-500 text-xs mt-1">{{getFieldError('city')}}</p>
              </div>

              <!-- Postal Code -->
              <div>
                <label for="postalCode" class="block text-sm font-medium text-gray-700 mb-1">Postal Code *</label>
                <input type="text" id="postalCode" formControlName="postalCode"
                       class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                       [class.border-red-500]="isFieldInvalid('postalCode')"
                       placeholder="Enter postal code">
                <p *ngIf="isFieldInvalid('postalCode')" class="text-red-500 text-xs mt-1">{{getFieldError('postalCode')}}</p>
              </div>

              <!-- Country -->
              <div>
                <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                <select id="country" formControlName="country"
                        class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        [class.border-red-500]="isFieldInvalid('country')">
                  <option value="Nepal">Nepal</option>
                  <option value="India">India</option>
                  <option value="Bangladesh">Bangladesh</option>
                  <option value="Other">Other</option>
                </select>
                <p *ngIf="isFieldInvalid('country')" class="text-red-500 text-xs mt-1">{{getFieldError('country')}}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="mb-8">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
          
          <!-- Interests -->
          <div class="mb-4">
            <label for="interests" class="block text-sm font-medium text-gray-700 mb-1">Interests in Mithila Art</label>
            <textarea id="interests" formControlName="interests" rows="2"
                      class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                      placeholder="Tell us about your interests in Mithila art, workshops you'd like to attend, etc."></textarea>
          </div>

          <!-- How did you hear -->
          <div>
            <label for="howDidYouHear" class="block text-sm font-medium text-gray-700 mb-1">How did you hear about us?</label>
            <select id="howDidYouHear" formControlName="howDidYouHear"
                    class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent">
              <option value="">Select an option</option>
              <option value="website">Website</option>
              <option value="social-media">Social Media</option>
              <option value="friend">Friend/Family</option>
              <option value="event">Event/Exhibition</option>
              <option value="newspaper">Newspaper/Magazine</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>

        <!-- Terms and Conditions -->
        <div class="mb-6">
          <div class="flex items-start">
            <input type="checkbox" id="agreeToTerms" formControlName="agreeToTerms"
                   class="mt-1 text-primary-600 focus:ring-primary-500"
                   [class.border-red-500]="isFieldInvalid('agreeToTerms')">
            <label for="agreeToTerms" class="ml-2 text-sm text-gray-700">
              I agree to the <a href="#" class="text-primary-600 hover:text-primary-700">Terms and Conditions</a> and <a href="#" class="text-primary-600 hover:text-primary-700">Privacy Policy</a> *
            </label>
          </div>
          <p *ngIf="isFieldInvalid('agreeToTerms')" class="text-red-500 text-xs mt-1">{{getFieldError('agreeToTerms')}}</p>
          
          <div class="flex items-start mt-2">
            <input type="checkbox" id="agreeToNewsletter" formControlName="agreeToNewsletter"
                   class="mt-1 text-primary-600 focus:ring-primary-500">
            <label for="agreeToNewsletter" class="ml-2 text-sm text-gray-700">
              I would like to receive newsletters and updates about events and exhibitions
            </label>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-4">
          <button type="button" 
                  (click)="closeForm()"
                  class="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200">
            Cancel
          </button>
          <button type="submit" 
                  [disabled]="isSubmitting"
                  class="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 flex items-center">
            <svg *ngIf="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{isSubmitting ? 'Submitting...' : 'Submit Application'}}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
