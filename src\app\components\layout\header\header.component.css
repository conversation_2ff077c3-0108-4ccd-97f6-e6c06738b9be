/* Enhanced <PERSON><PERSON> Styles with <PERSON><PERSON><PERSON> Art Inspiration */

/* Gradient border animation */
@keyframes borderGradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.border-gradient-animated {
  background: linear-gradient(90deg, #C1440E, #F4B400, #264653, #C1440E);
  background-size: 200% 200%;
  animation: borderGradient 4s ease infinite;
}

/* Logo hover effects */
.logo-glow {
  filter: drop-shadow(0 0 8px rgba(193, 68, 14, 0.3));
  transition: filter 0.2s ease;
}

.logo-glow:hover {
  filter: drop-shadow(0 0 12px rgba(193, 68, 14, 0.5));
}

/* Navigation link active state */
.nav-link-active {
  position: relative;
}

/* Active navigation link styling */
.nav-active div {
  width: 100% !important;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  /* Mobile header adjustments */
  .header-container {
    padding: 0.75rem 1rem;
  }

  /* Mobile logo sizing */
  .logo-container {
    max-width: 120px;
  }

  /* Mobile navigation improvements */
  .mobile-nav {
    max-height: calc(100vh - 80px);
    overflow-y: auto;
  }

  /* Mobile menu items */
  .mobile-nav-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(193, 68, 14, 0.1);
  }

  /* Mobile cart icon */
  .mobile-cart-icon {
    padding: 0.5rem;
  }

  /* Mobile hamburger menu */
  .hamburger-menu {
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
  }

  .hamburger-menu:hover {
    background-color: rgba(193, 68, 14, 0.1);
  }
}

@media (max-width: 480px) {
  /* Extra small mobile adjustments */
  .header-container {
    padding: 0.5rem 0.75rem;
  }

  .logo-container {
    max-width: 100px;
  }

  .mobile-nav-item {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }
}

/* Tablet responsive design */
@media (min-width: 769px) and (max-width: 1024px) {
  .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }

  .cart-icon {
    padding: 0.75rem;
  }
}

/* Large screen optimizations */
@media (min-width: 1440px) {
  .header-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  .nav-link {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
}

.nav-link-active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 6px;
  height: 6px;
  background: linear-gradient(45deg, #C1440E, #F4B400);
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(193, 68, 14, 0.3);
}

/* Button shine animation */
@keyframes buttonShine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.btn-shine {
  position: relative;
  overflow: hidden;
}

.btn-shine::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.3s ease;
}

.btn-shine:hover::before {
  left: 100%;
}

/* Cart badge pulse animation */
@keyframes cartPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.cart-badge {
  animation: cartPulse 2s infinite;
}

/* Mobile menu slide animation */
.mobile-menu-enter {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Decorative elements */
.decorative-dot {
  width: 4px;
  height: 4px;
  background: linear-gradient(45deg, #C1440E, #F4B400);
  border-radius: 50%;
  opacity: 0.6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mobile-nav-item {
    transform: translateX(-10px);
    opacity: 0;
    animation: slideInLeft 0.3s ease-out forwards;
  }

  .mobile-nav-item:nth-child(1) { animation-delay: 0.1s; }
  .mobile-nav-item:nth-child(2) { animation-delay: 0.2s; }
  .mobile-nav-item:nth-child(3) { animation-delay: 0.3s; }
  .mobile-nav-item:nth-child(4) { animation-delay: 0.4s; }
  .mobile-nav-item:nth-child(5) { animation-delay: 0.5s; }
  .mobile-nav-item:nth-child(6) { animation-delay: 0.6s; }
}

@keyframes slideInLeft {
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Header background pattern */
.header-pattern {
  background-image:
    radial-gradient(circle at 20% 50%, rgba(193, 68, 14, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(244, 180, 0, 0.1) 0%, transparent 50%);
}

/* Specific transitions for navigation elements */
.nav-link {
  transition: color 0.15s ease;
}

.nav-link:hover {
  transition: color 0.15s ease;
}

/* Custom scrollbar for mobile menu */
.mobile-nav::-webkit-scrollbar {
  width: 4px;
}

.mobile-nav::-webkit-scrollbar-track {
  background: rgba(193, 68, 14, 0.1);
}

.mobile-nav::-webkit-scrollbar-thumb {
  background: rgba(193, 68, 14, 0.3);
  border-radius: 2px;
}

.mobile-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(193, 68, 14, 0.5);
}
