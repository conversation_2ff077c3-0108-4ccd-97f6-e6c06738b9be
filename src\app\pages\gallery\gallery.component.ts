import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeroBannerComponent } from '../../components/shared/hero-banner/hero-banner.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';
import { LightboxComponent } from '../../components/shared/lightbox/lightbox.component';
import { DataService, LightboxImage } from '../../services/data.service';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-gallery',
  standalone: true,
  imports: [
    CommonModule,
    SectionTitleComponent,
    MithilaSectionComponent,
    MithilaArtBackgroundComponent,
    MithilaDecorativeElementComponent,
    LightboxComponent
  ],
  templateUrl: './gallery.component.html',
  styleUrl: './gallery.component.css'
})
export class GalleryComponent implements OnInit {
  selectedCategory = 'All';
  isLightboxOpen = false;
  currentImageIndex = 0;
  categories: string[] = [];
  galleryImages: LightboxImage[] = [];

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.categories = this.dataService.getGalleryCategories();
    this.galleryImages = this.dataService.getGalleryImages();
  }

  get filteredImages(): LightboxImage[] {
    return this.dataService.getGalleryImagesByCategory(this.selectedCategory);
  }

  selectCategory(category: string) {
    this.selectedCategory = category;
  }

  openLightbox(index: number) {
    this.currentImageIndex = index;
    this.isLightboxOpen = true;
  }

  closeLightbox() {
    this.isLightboxOpen = false;
  }

  onImageIndexChange(index: number) {
    this.currentImageIndex = index;
  }
}
