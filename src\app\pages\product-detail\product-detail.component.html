<div *ngIf="product" class="min-h-screen bg-gradient-to-br from-gray-50 to-white">
  <!-- Enhanced Breadcrumb -->
  <div class="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 sticky top-0 z-40">
    <div class="container mx-auto px-4 py-4">
      <nav class="flex items-center justify-between">
        <div class="flex items-center space-x-2 text-sm">
          <a routerLink="/" class="text-primary-600 hover:text-primary-700 transition-colors duration-200 font-medium">Home</a>
          <span class="text-gray-400">/</span>
          <a routerLink="/products" class="text-primary-600 hover:text-primary-700 transition-colors duration-200 font-medium">Products</a>
          <span class="text-gray-400">/</span>
          <span class="text-gray-600 font-medium">{{product.name}}</span>
        </div>

        <!-- Back Button -->
        <a routerLink="/products"
           class="flex items-center space-x-2 text-gray-600 hover:text-primary-600 transition-colors duration-200 bg-white/50 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/50 hover:border-primary-200">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          <span class="text-sm font-medium">Back to Products</span>
        </a>
      </nav>
    </div>
  </div>

  <!-- Product Details -->
  <div class="container mx-auto px-4 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
      <!-- Product Images -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Main Image -->
        <div class="relative aspect-square overflow-hidden rounded-2xl shadow-2xl bg-white p-4 group">
          <div class="relative h-full w-full overflow-hidden rounded-xl">
            <img
              [src]="product.images[selectedImageIndex]"
              [alt]="product.name"
              class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105">

            <!-- Gradient Overlay on Hover -->
            <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <!-- Zoom Icon -->
            <div class="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-x-2 group-hover:translate-x-0 cursor-pointer">
              <svg class="h-5 w-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7"/>
              </svg>
            </div>
          </div>

          <!-- Image Navigation Dots -->
          <div *ngIf="product.images.length > 1" class="absolute bottom-6 left-1/2 transform -translate-x-1/2">
            <div class="flex space-x-3 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg">
              <button
                *ngFor="let image of product.images; let i = index"
                (click)="selectImage(i)"
                [class]="selectedImageIndex === i ? 'bg-primary-600 scale-125' : 'bg-gray-300 hover:bg-gray-400'"
                class="w-3 h-3 rounded-full transition-all duration-300">
              </button>
            </div>
          </div>
        </div>

        <!-- Thumbnail Images -->
        <div *ngIf="product.images.length > 1" class="grid grid-cols-4 gap-3">
          <button
            *ngFor="let image of product.images; let i = index"
            (click)="selectImage(i)"
            [class]="selectedImageIndex === i ? 'ring-3 ring-primary-500 ring-offset-2 shadow-lg' : 'ring-1 ring-gray-200 hover:ring-2 hover:ring-primary-300 hover:shadow-md'"
            class="aspect-square overflow-hidden rounded-xl transition-all duration-300 transform hover:scale-105">
            <img [src]="image" [alt]="product.name" class="w-full h-full object-cover">
          </button>
        </div>
      </div>

      <!-- Product Information -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Product Header -->
        <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <div class="flex items-center gap-3 mb-4">
            <span class="px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full text-sm font-bold shadow-md">
              {{product.category}}
            </span>
            <span *ngIf="product.featured"
                  class="px-4 py-2 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white rounded-full text-sm font-bold shadow-md animate-pulse">
              ⭐ Featured
            </span>
          </div>

          <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">{{product.name}}</h1>

          <div class="flex items-center justify-between mb-6">
            <div class="flex items-baseline space-x-2">
              <span class="text-4xl font-bold text-primary-600">{{formatPrice(product.price)}}</span>
              <span class="text-lg text-gray-500">per piece</span>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-500">By</div>
              <div class="font-semibold text-gray-900">{{product.artist}}</div>
            </div>
          </div>
        </div>

        <!-- Product Description -->
        <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">About This Piece</h2>
          <div class="prose prose-gray max-w-none">
            <p class="text-lg text-gray-700 leading-relaxed mb-4">{{product.description}}</p>
            <p *ngIf="product.detailedDescription" class="text-gray-600">
              {{product.detailedDescription}}
            </p>
          </div>
        </div>

        <!-- Quantity and Add to Cart -->
        <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300">
          <h2 class="text-2xl font-bold text-gray-900 mb-6">Add to Cart</h2>

          <!-- Quantity Selector -->
          <div class="flex items-center space-x-6 mb-6">
            <div class="flex items-center space-x-3">
              <span class="text-lg font-medium text-gray-700">Quantity:</span>
              <div class="flex items-center border-2 border-gray-300 rounded-lg overflow-hidden">
                <button
                  (click)="decreaseQuantity()"
                  class="px-4 py-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors">
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                  </svg>
                </button>
                <span class="px-4 py-2 text-lg font-semibold text-gray-900 min-w-[3rem] text-center bg-gray-50">{{quantity}}</span>
                <button
                  (click)="increaseQuantity()"
                  class="px-4 py-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 transition-colors">
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Total Price -->
            <div class="flex-1 text-right">
              <div class="text-sm text-gray-500">Total</div>
              <div class="text-2xl font-bold text-primary-600">{{formatPrice(getTotalPrice())}}</div>
            </div>
          </div>

          <!-- Add to Cart Button -->
          <div class="space-y-4">
            <button
              (click)="addToCart()"
              class="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white px-8 py-4 rounded-xl font-bold text-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
              <div class="flex items-center justify-center space-x-3">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
                </svg>
                <span>Add to Cart</span>
              </div>
            </button>

            <!-- Already in Cart Indicator -->
            <div *ngIf="isInCart()" class="text-center">
              <div class="inline-flex items-center space-x-2 text-green-600 bg-green-50 px-4 py-2 rounded-lg">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                <span class="font-medium">{{getCartQuantity()}} item(s) in cart</span>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Product Details Grid - Full Width -->
    <div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Artist Information -->
      <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
        <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <div class="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center mr-3">
            <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
            </svg>
          </div>
          Artist Information
        </h3>
        <p class="text-lg font-semibold text-gray-900 mb-3">{{product.artist}}</p>
        <p *ngIf="product.artistBio" class="text-gray-600 leading-relaxed">{{product.artistBio}}</p>
      </div>

      <!-- Specifications -->
      <div class="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-shadow duration-300">
        <h3 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
          <div class="w-10 h-10 bg-secondary-100 rounded-full flex items-center justify-center mr-3">
            <svg class="h-5 w-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
            </svg>
          </div>
          Specifications
        </h3>
        <div class="space-y-4">
          <div class="flex justify-between items-center py-2 border-b border-gray-100">
            <span class="text-gray-600 font-medium">Dimensions:</span>
            <span class="font-semibold text-gray-900">{{product.dimensions}}</span>
          </div>
          <div class="py-2">
            <span class="text-gray-600 font-medium block mb-2">Materials:</span>
            <div class="flex flex-wrap gap-2">
              <span *ngFor="let material of product.materials"
                    class="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium">
                {{material}}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Cultural Significance -->
      <div *ngIf="product.culturalSignificance" class="bg-gradient-to-r from-amber-50 to-orange-50 p-8 rounded-2xl border border-amber-200 shadow-lg hover:shadow-xl transition-shadow duration-300">
        <h3 class="text-xl font-bold text-amber-900 mb-4 flex items-center">
          <div class="w-10 h-10 bg-amber-200 rounded-full flex items-center justify-center mr-3">
            <svg class="h-5 w-5 text-amber-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
          </div>
          Cultural Significance
        </h3>
        <p class="text-amber-800 leading-relaxed">{{product.culturalSignificance}}</p>
      </div>
    </div>
  </div>

  <!-- Enhanced Cart Success Modal -->
  <div *ngIf="showCartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
    <div class="bg-white rounded-2xl p-8 max-w-md mx-4 transform transition-all duration-300 scale-100 animate-scale-in shadow-2xl">
      <div class="text-center">
        <div class="w-16 h-16 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4 animate-bounce">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-900 mb-2">Added to Cart!</h3>
        <p class="text-gray-600 mb-6">{{quantity}} item(s) of "{{product.name}}" added to your cart.</p>

        <div class="flex gap-3">
          <button
            (click)="showCartModal = false"
            class="flex-1 px-4 py-2 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-300 font-medium">
            Continue Shopping
          </button>
          <button
            (click)="viewCart()"
            class="flex-1 px-4 py-2 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 font-medium shadow-lg">
            View Cart
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
