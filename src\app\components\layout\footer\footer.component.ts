import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { CommonModule } from '@angular/common';
import { MithilaArtBackgroundComponent } from '../../shared/mithila-art-background/mithila-art-background.component';
import { MithilaDecorativeElementComponent } from '../../shared/mithila-decorative-element/mithila-decorative-element.component';
import { MithilaBorderComponent } from '../../shared/mithila-border/mithila-border.component';

@Component({
  selector: 'app-footer',
  standalone: true,
  imports: [
    RouterLink,
    CommonModule,
    MithilaArtBackgroundComponent,
    MithilaDecorativeElementComponent,
    MithilaBorderComponent
  ],
  templateUrl: './footer.component.html',
  styleUrl: './footer.component.css'
})
export class FooterComponent {
  currentYear = new Date().getFullYear();
}
