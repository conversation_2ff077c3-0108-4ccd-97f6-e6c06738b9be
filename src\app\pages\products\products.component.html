<!-- Enhanced Hero Section with Floating Design -->
<div class="relative h-[60vh] md:h-[70vh] overflow-hidden">
  <!-- Background Image with Parallax Effect -->
  <div class="absolute inset-0 bg-cover bg-center bg-no-repeat transform transition-transform duration-10000 hover:scale-105"
       style="background-image: url('https://i.etsystatic.com/43638819/r/il/bba73f/5930357734/il_794xN.5930357734_1ql7.jpg');">
  </div>

  <!-- Enhanced Gradient Overlay -->
  <div class="absolute inset-0 bg-gradient-to-br from-primary-600/90 via-primary-500/80 to-secondary-600/90"></div>

  <!-- Mithila Art Background Pattern -->
  <div class="absolute inset-0 opacity-15">
    <app-mithila-art-background
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      opacity="20">
    </app-mithila-art-background>
  </div>

  <!-- Decorative Border -->
  <app-mithila-border
    [primaryColor]="'#C1440E'"
    [secondaryColor]="'#F4B400'"
    [type]="'full'"
    position="top-8 left-8 right-8 bottom-8">
  </app-mithila-border>

  <!-- Hero Content -->
  <div class="container mx-auto px-4 h-full flex flex-col justify-center items-center text-center relative z-10">
    <!-- Decorative Element -->
    <app-mithila-decorative-element
      [primaryColor]="'#C1440E'"
      [secondaryColor]="'#F4B400'"
      [type]="'lotus'"
      position="relative mb-6"
      classes="opacity-90 animate-float-slow"
      size="80px">
    </app-mithila-decorative-element>

    <div class="max-w-4xl mx-auto">
      <h1 class="text-5xl md:text-6xl font-bold text-white mb-6 font-display animate-fade-in">
        Our Art Collection
      </h1>
      <p class="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto">
        Discover authentic Mithila art pieces crafted by skilled local artists
      </p>
      <div class="inline-block px-6 py-3 bg-white/10 backdrop-blur-md rounded-full border border-white/30 animate-fade-in-delay-1">
        <span class="text-white font-medium">🎨 Handcrafted • 🌟 Authentic • 💝 Cultural Heritage</span>
      </div>
    </div>
  </div>
</div>

<!-- Products Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-gray-50 via-background-light to-primary-50"
  backgroundOpacity="8"
  padding="py-16 sm:py-20 md:py-24"
  [showDecorativeElements]="true">

  <div class="container mx-auto px-4">
    <!-- Section Header -->
    <app-section-title
      title="Browse Our Products"
      subtitle="Each piece tells a story of tradition, craftsmanship, and cultural heritage"
    ></app-section-title>

    <!-- Category Filter -->
    <div class="flex flex-wrap justify-center gap-4 mb-12">
      <button
        *ngFor="let category of categories"
        (click)="filterByCategory(category)"
        [class]="selectedCategory === category ?
          'bg-primary-600 text-white shadow-lg transform scale-105' :
          'bg-white text-gray-700 hover:bg-primary-50 hover:shadow-md hover:transform hover:scale-105'"
        class="px-6 py-3 rounded-full border border-primary-200 transition-all duration-300 font-medium">
        {{category}}
      </button>
    </div>

    <!-- Products Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Enhanced Product Card -->
      <div
        *ngFor="let product of filteredProducts; let i = index"
        class="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 overflow-hidden border border-gray-100 hover:border-primary-200 cursor-pointer animate-fade-in-up"
        [style.animation-delay]="(i * 100) + 'ms'"
        (click)="navigateToProduct(product.slug)">

        <!-- Product Image Container -->
        <div class="relative overflow-hidden aspect-square">
          <img
            [src]="product.images[0]"
            [alt]="product.name"
            class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

          <!-- Image Overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          <!-- Badges -->
          <div class="absolute top-4 left-4 flex flex-col space-y-2">
            <span *ngIf="product.featured"
                  class="bg-gradient-to-r from-secondary-500 to-secondary-600 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg animate-pulse flex items-center">
              <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
              </svg>
              Featured
            </span>

            <span *ngIf="product.category"
                  class="bg-white/90 backdrop-blur-sm text-primary-600 px-3 py-1 rounded-full text-xs font-medium shadow-md">
              {{product.category}}
            </span>
          </div>

          <!-- Quick Actions -->
          <div class="absolute bottom-4 right-4 flex space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
            <!-- Quick View Button -->
            <a [routerLink]="['/products', product.slug]"
               class="p-2 bg-white/90 backdrop-blur-sm rounded-full shadow-lg hover:bg-white hover:scale-110 transition-all duration-300 text-gray-600 hover:text-primary-600"
               (click)="$event.stopPropagation()">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            </a>
          </div>

          <!-- Decorative Element -->
          <app-mithila-decorative-element
            [primaryColor]="'#F4B400'"
            [secondaryColor]="'#C1440E'"
            [type]="'lotus'"
            position="absolute -bottom-2 -left-2"
            classes="opacity-10 group-hover:opacity-20 transition-opacity duration-300"
            size="40px">
          </app-mithila-decorative-element>
        </div>

        <!-- Content -->
        <div class="p-6">
          <!-- Title and Artist -->
          <div class="mb-4">
            <h3 class="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors duration-300 relative">
              <span class="relative">
                {{product.name}}
                <span class="absolute bottom-0 left-0 w-0 h-0.5 bg-primary-600 transition-all duration-300 group-hover:w-full"></span>
              </span>
            </h3>
            <p class="text-sm text-gray-600 flex items-center">
              <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              By <span class="text-primary-600 font-medium ml-1">{{product.artist}}</span>
            </p>
          </div>

          <!-- Description -->
          <p class="text-gray-600 mb-4 line-clamp-2">{{product.description}}</p>

          <!-- Details -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"/>
              </svg>
              {{product.dimensions}}
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
              </svg>
              {{product.materials.join(', ') || 'Handcrafted'}}
            </div>
          </div>

          <!-- Price and Actions -->
          <div class="flex items-center justify-between">
            <div class="flex flex-col">
              <span class="text-2xl font-bold text-primary-600 mb-1">{{formatPrice(product.price)}}</span>
              <span *ngIf="isInCart(product.id)" class="text-sm text-green-600 font-medium flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
                In Cart
              </span>
            </div>

            <!-- Add to Cart Button -->
            <button *ngIf="!isInCart(product.id)"
                    (click)="addToCart(product, $event)"
                    class="px-6 py-3 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-lg hover:from-primary-700 hover:to-primary-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-medium flex items-center">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
              </svg>
              Add to Cart
            </button>

            <!-- Already in Cart Button -->
            <button *ngIf="isInCart(product.id)"
                    class="px-6 py-3 bg-green-600 text-white rounded-lg font-medium flex items-center cursor-default">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
              </svg>
              Added to Cart
            </button>
          </div>
        </div>

        <!-- Hover Glow Effect -->
        <div class="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
             style="box-shadow: 0 0 30px rgba(193, 68, 14, 0.2);"></div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="filteredProducts.length === 0" class="text-center py-16">
      <div class="max-w-md mx-auto">
        <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-full flex items-center justify-center">
          <svg class="h-12 w-12 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
          </svg>
        </div>
        <h3 class="text-xl font-medium text-gray-900 mb-2">No products found</h3>
        <p class="text-gray-500 mb-6">Try selecting a different category or check back later for new arrivals.</p>
        <button
          (click)="filterByCategory('All')"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors duration-300 font-medium">
          View All Products
        </button>
      </div>
    </div>
  </div>
</app-mithila-section>

<!-- Call to Action Section with Enhanced Floating Design -->
<app-mithila-section
  primaryColor="#C1440E"
  secondaryColor="#F4B400"
  backgroundGradient="from-primary-600 via-primary-700 to-secondary-600"
  backgroundOpacity="15"
  padding="py-16 sm:py-20 md:py-24"
  classes="bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600"
  [showDecorativeElements]="true">

  <div class="text-center text-white">
    <app-section-title
      title="Can't Find What You're Looking For?"
      subtitle="We create custom Mithila art pieces tailored to your vision. Contact us to discuss your specific requirements and bring your ideas to life."
      classes="text-white"
    ></app-section-title>

    <div class="flex flex-wrap justify-center gap-4 mt-8">
      <a routerLink="/contact"
         class="bg-white text-primary-600 px-8 py-4 rounded-full font-bold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
        <span class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          Contact Us for Custom Orders
        </span>
      </a>
      <a routerLink="/artists"
         class="bg-white/10 backdrop-blur-md text-white px-8 py-4 rounded-full font-bold hover:bg-white/20 transition-all duration-300 transform hover:scale-105 border border-white/30">
        <span class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          Meet Our Artists
        </span>
      </a>
    </div>
  </div>
</app-mithila-section>
