import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { CartService, CartItem } from '../../services/cart.service';
import { DataService, Product } from '../../services/data.service';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';
import { MithilaArtBackgroundComponent } from '../../components/shared/mithila-art-background/mithila-art-background.component';
import { MithilaBorderComponent } from '../../components/shared/mithila-border/mithila-border.component';
import { MithilaDecorativeElementComponent } from '../../components/shared/mithila-decorative-element/mithila-decorative-element.component';

@Component({
  selector: 'app-products',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MithilaSectionComponent,
    SectionTitleComponent,
    MithilaArtBackgroundComponent,
    MithilaBorderComponent,
    MithilaDecorativeElementComponent
  ],
  templateUrl: './products.component.html',
  styleUrls: ['./products.component.css']
})
export class ProductsComponent implements OnInit {
  products: Product[] = [];
  filteredProducts: Product[] = [];
  selectedCategory: string = 'All';
  categories: string[] = [];

  constructor(
    private cartService: CartService,
    private router: Router,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.products = this.dataService.getProducts();
    this.categories = this.dataService.getProductCategories();
    this.filteredProducts = this.products;
  }

  filterByCategory(category: string) {
    this.selectedCategory = category;
    if (category === 'All') {
      this.filteredProducts = this.products;
    } else {
      this.filteredProducts = this.products.filter(product => product.category === category);
    }
  }

  addToCart(product: Product, event: Event) {
    event.preventDefault();
    event.stopPropagation();

    const cartItem: Omit<CartItem, 'quantity'> = {
      id: product.id,
      name: product.name,
      slug: product.slug,
      price: product.price,
      image: product.images[0],
      artist: product.artist
    };

    this.cartService.addToCart(cartItem, 1);
  }

  isInCart(productId: string): boolean {
    return this.cartService.isInCart(productId);
  }

  formatPrice(price: number): string {
    return `NPR ${price.toLocaleString()}`;
  }

  navigateToProduct(slug: string) {
    this.router.navigate(['/products', slug]);
  }
}
