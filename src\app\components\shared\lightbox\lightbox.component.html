<!-- Lightbox Overlay -->
<div *ngIf="isOpen" 
     class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90"
     [@fadeInOut]
     (click)="onBackdropClick($event)">
  
  <!-- Close Button -->
  <button 
    class="absolute top-4 right-4 z-60 text-white hover:text-gray-300 transition-colors duration-200"
    (click)="closeLightbox()"
    aria-label="Close lightbox">
    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
    </svg>
  </button>

  <!-- Navigation Buttons -->
  <button *ngIf="hasPrevious"
    class="absolute left-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors duration-200 p-2"
    (click)="previousImage()"
    aria-label="Previous image">
    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
    </svg>
  </button>

  <button *ngIf="hasNext"
    class="absolute right-4 top-1/2 transform -translate-y-1/2 z-60 text-white hover:text-gray-300 transition-colors duration-200 p-2"
    (click)="nextImage()"
    aria-label="Next image">
    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
  </button>

  <!-- Main Content -->
  <div class="max-w-7xl max-h-full mx-4 flex flex-col items-center" [@slideIn]>
    <!-- Image Container -->
    <div class="relative flex-1 flex items-center justify-center mb-4">
      <img *ngIf="currentImage"
           [src]="currentImage.src"
           [alt]="currentImage.alt"
           class="max-w-full lightbox-max-height object-contain shadow-2xl"
           (load)="onImageLoad()"
           (error)="onImageError()">
      
      <!-- Loading Spinner -->
      <div *ngIf="isLoading" class="absolute inset-0 flex items-center justify-center">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    </div>

    <!-- Image Info -->
    <div *ngIf="currentImage" class="relative flex items-center justify-center">
      <!-- Image Title -->
      <div class="text-center text-white">
        <h3 *ngIf="currentImage.title" class="text-xl font-bold">{{ currentImage.title }}</h3>
      </div>

      <!-- Info Button - positioned at top right of image -->
      <button
        class="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center hover:bg-white/30 transition-all duration-200"
        (click)="toggleDescription()"
        aria-label="Show description">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </button>

      <!-- Description Popup - constrained to image width -->
      <div *ngIf="showDescription && currentImage"
           class="absolute top-0 left-0 right-0 bg-white/95 backdrop-blur-sm rounded-lg p-4 text-gray-900 shadow-lg transform -translate-y-full"
           [@fadeInOut]>
        <div class="flex justify-between items-start mb-2">
          <h4 class="font-bold text-lg">{{ currentImage.title }}</h4>
          <button
            class="text-gray-500 hover:text-gray-700 ml-2"
            (click)="toggleDescription()"
            aria-label="Close description">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <p *ngIf="currentImage.artist" class="text-primary-600 font-medium mb-2">By {{ currentImage.artist }}</p>
        <p *ngIf="currentImage.category" class="text-sm text-gray-600 mb-2">Category: {{ currentImage.category }}</p>
        <p *ngIf="currentImage.description" class="text-gray-700 text-sm">{{ currentImage.description }}</p>
      </div>
    </div>

    <!-- Image Counter -->
    <div class="text-white text-sm mt-4">
      {{ currentIndex + 1 }} of {{ images.length }}
    </div>

    <!-- Thumbnail Navigation -->
    <div *ngIf="images.length > 1" class="flex space-x-2 mt-4 max-w-full overflow-x-auto pb-2">
      <button *ngFor="let image of images; let i = index"
              class="flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200"
              [class.border-white]="i === currentIndex"
              [class.border-gray-600]="i !== currentIndex"
              [class.opacity-100]="i === currentIndex"
              [class.opacity-60]="i !== currentIndex"
              (click)="goToImage(i)">
        <img [src]="image.src" [alt]="image.alt" class="w-full h-full object-cover">
      </button>
    </div>
  </div>

  <!-- Keyboard Instructions -->
  <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm text-center opacity-70">
    <p>Use arrow keys to navigate • Press ESC to close</p>
  </div>
</div>
