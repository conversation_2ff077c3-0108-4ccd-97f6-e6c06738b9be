import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { MithilaSectionComponent } from '../../components/shared/mithila-section/mithila-section.component';
import { SectionTitleComponent } from '../../components/shared/section-title/section-title.component';

@Component({
  selector: 'app-privacy-policy',
  standalone: true,
  imports: [
    CommonModule,
    RouterLink,
    MithilaSectionComponent,
    SectionTitleComponent
  ],
  templateUrl: './privacy-policy.component.html',
  styleUrl: './privacy-policy.component.css'
})
export class PrivacyPolicyComponent {
  lastUpdated = 'December 21, 2024';
}
